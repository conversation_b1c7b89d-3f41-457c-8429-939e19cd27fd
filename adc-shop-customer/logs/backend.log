Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-06T10:12:22+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-06T10:12:22+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-06T10:12:22+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> customer-backend/internal/api/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/register     --> customer-backend/internal/api/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/oauth-user   --> customer-backend/internal/api/handlers.(*AuthHandler).CreateOAuthCustomer-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> customer-backend/internal/api/handlers.(*AuthHandler).Logout-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> customer-backend/internal/api/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/forgot-password --> customer-backend/internal/api/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/reset-password --> customer-backend/internal/api/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug --> customer-backend/internal/api/handlers.(*ShopHandler).GetBranchBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] POST   /orders/create-with-payment --> customer-backend/internal/api/handlers.(*OrderHandler).CreateOrderWithPayment-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-06T10:12:22+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:12:35+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:12:35+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:12:35+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:12:35+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:12:35+07:00"}

2025/06/06 10:12:35 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[443.835ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:48 [33mSLOW SQL >= 200ms
[0m[31;1m[560.563ms] [33m[rows:1][35m INSERT INTO "cart_sessions" ("user_id","session_id","expires_at","created_at","updated_at","deleted_at") VALUES (NULL,'guest_1749179554604_u453qxvol','2025-06-07 10:12:35.817','2025-06-06 10:12:35.929','2025-06-06 10:12:35.929',NULL) RETURNING "id"[0m

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[1125.915ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[1091.591ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[1138.856ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:12:36 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 1.091918916s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:12:36+07:00"}

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:375 [33mSLOW SQL >= 200ms
[0m[31;1m[329.673ms] [33m[rows:2][35m SELECT DISTINCT "price_range" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:55 [33mSLOW SQL >= 200ms
[0m[31;1m[252.209ms] [33m[rows:0][35m SELECT * FROM "cart_items" WHERE "cart_items"."cart_session_id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_items"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:12:36 +07] \"GET /shops/filter-options HTTP/1.1 200 1.4707595s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:12:36+07:00"}

2025/06/06 10:12:36 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:55 [33mSLOW SQL >= 200ms
[0m[31;1m[465.871ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE "cart_sessions"."id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_sessions"."deleted_at" IS NULL AND "cart_sessions"."id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:12:36 +07] \"GET /cart HTTP/1.1 200 1.471479708s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:12:36+07:00"}

2025/06/06 10:12:37 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[512.729ms] [33m[rows:5][35m SELECT * FROM "shop_branches" WHERE "shop_branches"."shop_id" IN ('550e8400-e29b-41d4-a716-446655440001','672023d9-8971-43fb-b09e-fe41bbb55ec7') AND is_active = true AND "shop_branches"."deleted_at" IS NULL[0m

2025/06/06 10:12:37 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[844.198ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:12:37+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:12:37+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:12:37 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 1.971610833s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:12:37+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-06T10:13:04+07:00"}

2025/06/06 10:13:04 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:399 [33mSLOW SQL >= 200ms
[0m[31;1m[580.618ms] [33m[rows:1][35m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1[0m
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: downtown","time":"2025-06-06T10:13:04+07:00"}

2025/06/06 10:13:05 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:321 [33mSLOW SQL >= 200ms
[0m[31;1m[242.994ms] [33m[rows:1][35m SELECT count(*) FROM "menu_items" WHERE (branch_id = '550e8400-e29b-41d4-a716-446655440101' AND is_available = true) AND is_available = true[0m

2025/06/06 10:13:06 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[1223.146ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = '550e8400-e29b-41d4-a716-446655440101' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m

2025/06/06 10:13:06 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:688 [33mSLOW SQL >= 200ms
[0m[31;1m[590.756ms] [33m[rows:1][35m SELECT id, options FROM "menu_items" WHERE id = '94f2bbfe-8f11-4e63-8f93-c0307a2c1935' ORDER BY "menu_items"."id" LIMIT 1[0m

2025/06/06 10:13:07 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:688 [33mSLOW SQL >= 200ms
[0m[31;1m[542.152ms] [33m[rows:1][35m SELECT id, options FROM "menu_items" WHERE id = '0f98f70c-5bb2-4a6b-b731-3a60208a3475' ORDER BY "menu_items"."id" LIMIT 1[0m

2025/06/06 10:13:07 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:461 [33mSLOW SQL >= 200ms
[0m[31;1m[253.554ms] [33m[rows:4][35m SELECT * FROM "menu_categories" WHERE branch_id = '550e8400-e29b-41d4-a716-446655440101' AND is_active = true ORDER BY sort_order ASC, name ASC[0m

2025/06/06 10:13:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:473 [33mSLOW SQL >= 200ms
[0m[31;1m[435.876ms] [33m[rows:1][35m SELECT count(*) FROM "menu_items" WHERE category_id = 'e45fdbd4-0977-44e0-8a2c-d659e4dedce1' AND is_available = true[0m

2025/06/06 10:13:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:473 [33mSLOW SQL >= 200ms
[0m[31;1m[321.713ms] [33m[rows:1][35m SELECT count(*) FROM "menu_items" WHERE category_id = '869cdeb1-fc99-4c66-aeb7-4dcafcef1a48' AND is_available = true[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:13:08 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 4.875185459s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:13:08+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:15:09+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:15:09+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:15:09+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:15:09+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:15:09+07:00"}

2025/06/06 10:15:09 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[622.808ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:15:09 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[535.077ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m

2025/06/06 10:15:09 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[589.553ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:15:09 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 535.336833ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:15:09+07:00"}

2025/06/06 10:15:10 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:375 [33mSLOW SQL >= 200ms
[0m[31;1m[287.918ms] [33m[rows:2][35m SELECT DISTINCT "price_range" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:15:10 +07] \"GET /shops/filter-options HTTP/1.1 200 910.988ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:15:10+07:00"}

2025/06/06 10:15:10 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[289.495ms] [33m[rows:0][35m SELECT * FROM "cart_items" WHERE "cart_items"."cart_session_id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_items"."deleted_at" IS NULL[0m

2025/06/06 10:15:10 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[953.410ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:15:10 +07] \"GET /cart HTTP/1.1 200 953.471583ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:15:10+07:00"}

2025/06/06 10:15:10 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[545.164ms] [33m[rows:5][35m SELECT * FROM "shop_branches" WHERE "shop_branches"."shop_id" IN ('550e8400-e29b-41d4-a716-446655440001','672023d9-8971-43fb-b09e-fe41bbb55ec7') AND is_active = true AND "shop_branches"."deleted_at" IS NULL[0m

2025/06/06 10:15:10 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[834.488ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:15:10+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:15:10+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:15:10 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 1.424569666s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:15:10+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:20:29+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:20:29+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:20:29+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:20:29+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:20:29+07:00"}

2025/06/06 10:20:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[458.829ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:20:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[496.462ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:20:30 +07] \"GET /shops/filter-options HTTP/1.1 200 653.258917ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:20:30+07:00"}

2025/06/06 10:20:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[537.225ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m

2025/06/06 10:20:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[707.672ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:20:30 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 537.442125ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:20:30+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:20:30 +07] \"GET /cart HTTP/1.1 200 707.991334ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:20:30+07:00"}

2025/06/06 10:20:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[273.429ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:20:30+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:20:30+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:20:30 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 732.7725ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:20:30+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:21:07+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:21:07+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:21:07+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:21:07+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:21:07+07:00"}

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:158 [33mSLOW SQL >= 200ms
[0m[31;1m[463.251ms] [33m[rows:0][35m SELECT * FROM "orders" WHERE customer_phone = 'guest_1749179554604_u453qxvol' AND "orders"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 50[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:08 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 463.524708ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:08+07:00"}

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:350 [33mSLOW SQL >= 200ms
[0m[31;1m[556.014ms] [33m[rows:2][35m SELECT DISTINCT "cuisine_type" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:138 [33mSLOW SQL >= 200ms
[0m[31;1m[795.683ms] [33m[rows:1][35m SELECT count(*) FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:375 [33mSLOW SQL >= 200ms
[0m[31;1m[275.952ms] [33m[rows:2][35m SELECT DISTINCT "price_range" FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:08 +07] \"GET /shops/filter-options HTTP/1.1 200 832.57075ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:08+07:00"}

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[462.876ms] [33m[rows:0][35m SELECT * FROM "cart_items" WHERE "cart_items"."cart_session_id" = 'bb7f265b-4935-4c69-be81-65b9c2a2b2df' AND "cart_items"."deleted_at" IS NULL[0m

2025/06/06 10:21:08 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[1084.394ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749179554604_u453qxvol' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:08 +07] \"GET /cart HTTP/1.1 200 1.08447725s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:08+07:00"}

2025/06/06 10:21:09 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[373.145ms] [33m[rows:5][35m SELECT * FROM "shop_branches" WHERE "shop_branches"."shop_id" IN ('550e8400-e29b-41d4-a716-446655440001','672023d9-8971-43fb-b09e-fe41bbb55ec7') AND is_active = true AND "shop_branches"."deleted_at" IS NULL[0m

2025/06/06 10:21:09 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[798.237ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:21:09+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:21:09+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:09 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 1.594754459s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:09+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-06T10:21:17+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: downtown","time":"2025-06-06T10:21:17+07:00"}

2025/06/06 10:21:17 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[205.182ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = '550e8400-e29b-41d4-a716-446655440101' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:17 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 981.401334ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:17+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: riverside, page=1, limit=20","time":"2025-06-06T10:21:20+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: riverside","time":"2025-06-06T10:21:20+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:21 +07] \"GET /shops/slug/thai-delight/branches/slug/riverside/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 584.460166ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:21+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: posriya, page=1, limit=20","time":"2025-06-06T10:21:24+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: posriya","time":"2025-06-06T10:21:24+07:00"}

2025/06/06 10:21:25 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[205.433ms] [33m[rows:0][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = '3119e413-0b03-4180-9295-697f0c82a367' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m

2025/06/06 10:21:25 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:444 [33mSLOW SQL >= 200ms
[0m[31;1m[245.035ms] [33m[rows:1][35m SELECT shop_branches.id FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE shops.slug = 'thai-delight' AND shop_branches.slug = 'posriya' AND shops.is_active = true AND shop_branches.is_active = true[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:25 +07] \"GET /shops/slug/thai-delight/branches/slug/posriya/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 857.188459ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:25+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: the-green-terrace, page=1, limit=20","time":"2025-06-06T10:21:30+07:00"}

2025/06/06 10:21:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:399 [33mSLOW SQL >= 200ms
[0m[31;1m[373.225ms] [33m[rows:1][35m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'the-green-terrace' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1[0m
{"level":"info","msg":"Looking for branch with shop slug: weerawat-poseeya, branch slug: the-green-terrace","time":"2025-06-06T10:21:30+07:00"}

2025/06/06 10:21:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:321 [33mSLOW SQL >= 200ms
[0m[31;1m[334.950ms] [33m[rows:1][35m SELECT count(*) FROM "menu_items" WHERE (branch_id = '5a76fb56-4c1c-441f-9396-0c3d4f13765a' AND is_available = true) AND is_available = true[0m

2025/06/06 10:21:32 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:688 [33mSLOW SQL >= 200ms
[0m[31;1m[211.213ms] [33m[rows:1][35m SELECT id, options FROM "menu_items" WHERE id = '2c25e2ed-ae33-435e-b100-2b307769e35e' ORDER BY "menu_items"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:21:32 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/the-green-terrace/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 2.446378666s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:21:32+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:26:12+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:26:12+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:26:12+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:26:12 +07] \"GET /cart HTTP/1.1 200 126.136333ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:26:12 +07] \"GET /orders/customer?customerPhone=guest_1749179554604_u453qxvol\u0026limit=50 HTTP/1.1 200 72.2175ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:26:12 +07] \"GET /shops/filter-options HTTP/1.1 200 204.690708ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:26:12+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:26:12+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:26:12 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 203.478417ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:26:12+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: posriya, page=1, limit=20","time":"2025-06-06T10:26:28+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: weerawat-poseeya, branch slug: posriya","time":"2025-06-06T10:26:28+07:00"}

2025/06/06 10:26:31 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[2582.773ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = 'c8143f1c-62f1-4a37-b312-c64b847203c6' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:26:32 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/posriya/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 3.2564485s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:26:32+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:32:40+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:32:40+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:32:40+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:32:40+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:32:40+07:00"}

2025/06/06 10:32:40 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[82.554ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749180759622_hcxcuumd3' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:32:40 +07] \"GET /shops/filter-options HTTP/1.1 200 120.549958ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:32:40+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:32:40 +07] \"GET /orders/customer?customerPhone=guest_1749180759622_hcxcuumd3\u0026limit=50 HTTP/1.1 200 77.421583ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:32:40+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:32:40+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:32:40+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:32:40 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 157.814875ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:32:40+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:32:40 +07] \"GET /cart HTTP/1.1 200 381.535ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:32:40+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:34:44+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:34:44+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:34:44+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:34:44+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:34:44+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:34:44 +07] \"GET /cart HTTP/1.1 200 149.963833ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:34:44+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:34:44 +07] \"GET /shops/filter-options HTTP/1.1 200 150.844959ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:34:44+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:34:44 +07] \"GET /orders/customer?customerPhone=guest_1749180759622_hcxcuumd3\u0026limit=50 HTTP/1.1 200 35.952333ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:34:44+07:00"}

2025/06/06 10:34:44 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:147 [33mSLOW SQL >= 200ms
[0m[31;1m[201.451ms] [33m[rows:2][35m SELECT * FROM "shops" WHERE is_active = true AND "shops"."deleted_at" IS NULL ORDER BY created_at DESC LIMIT 20[0m
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:34:44+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:34:44+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:34:44 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 299.679625ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:34:44+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-06T10:35:33+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-06T10:35:33+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-06T10:35:33+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-06T10:35:33+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-06T10:35:33+07:00"}
{"address_city":"Bangkok","address_street":"123 Sukhumvit Road","converting_shop_id":"550e8400-e29b-41d4-a716-446655440001","converting_shop_name":"Thai Delight Restaurant","converting_shop_slug":"thai-delight","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:35:34+07:00"}
{"address_city":"Bangkok","address_street":"61 Lat Phrao Road","converting_shop_id":"672023d9-8971-43fb-b09e-fe41bbb55ec7","converting_shop_name":"weerawat poseeya","converting_shop_slug":"weerawat-poseeya","level":"info","msg":"Converting shop to customer settings","time":"2025-06-06T10:35:34+07:00"}
{"level":"info","msg":"::1 - [Fri, 06 Jun 2025 10:35:34 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 231.703291ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-06T10:35:34+07:00"}
{"level":"info","msg":"Shutting down server...","time":"2025-06-06T10:39:18+07:00"}
{"level":"info","msg":"Customer API exited","time":"2025-06-06T10:39:18+07:00"}
make[2]: *** [run] Error 1
