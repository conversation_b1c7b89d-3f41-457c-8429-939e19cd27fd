Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-06T10:47:44+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-06T10:47:44+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-06T10:47:45+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> customer-backend/internal/api/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/register     --> customer-backend/internal/api/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/oauth-user   --> customer-backend/internal/api/handlers.(*AuthHandler).CreateOAuthCustomer-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> customer-backend/internal/api/handlers.(*AuthHandler).Logout-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> customer-backend/internal/api/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/forgot-password --> customer-backend/internal/api/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/reset-password --> customer-backend/internal/api/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug --> customer-backend/internal/api/handlers.(*ShopHandler).GetBranchBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] POST   /orders/create-with-payment --> customer-backend/internal/api/handlers.(*OrderHandler).CreateOrderWithPayment-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-06T10:47:45+07:00"}
